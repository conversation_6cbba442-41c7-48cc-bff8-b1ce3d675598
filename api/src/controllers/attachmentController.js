import { StatusCodes } from 'http-status-codes'
import { attachmentService } from '~/services/attachmentService'

const uploadAttachment = async (req, res, next) => {
  try {
    // Lấy cardId từ params và file từ request (đã được xử lý bởi middleware)
    const { cardId } = req.params
    const uploadedFile = req.file

    // Gọi service để xử lý upload file
    const uploadedAttachment = await attachmentService.uploadAttachment(cardId, uploadedFile)

    res.status(StatusCodes.CREATED).json(uploadedAttachment)
  } catch (error) {
    next(error)
  }
}

const getAttachments = async (req, res, next) => {
  try {
    const { cardId } = req.params
    const attachments = await attachmentService.getAttachmentsByCardId(cardId)

    res.status(StatusCodes.OK).json(attachments)
  } catch (error) {
    next(error)
  }
}

const deleteAttachment = async (req, res, next) => {
  try {
    const { id: attachmentId } = req.params
    const result = await attachmentService.deleteAttachment(attachmentId)

    res.status(StatusCodes.OK).json(result)
  } catch (error) {
    next(error)
  }
}

const downloadAttachment = async (req, res, next) => {
  try {
    const { id: attachmentId } = req.params
    const attachment = await attachmentService.getAttachmentById(attachmentId)

    // Redirect tới URL download của file (từ cloudinary)
    res.redirect(attachment.downloadUrl)
  } catch (error) {
    next(error)
  }
}

const previewAttachment = async (req, res, next) => {
  try {
    const { id: attachmentId } = req.params
    const attachment = await attachmentService.getAttachmentById(attachmentId)

    // Kiểm tra nếu là file ảnh
    if (!attachment.fileType.startsWith('image/')) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        message: 'File không phải là ảnh'
      })
    }

    // Redirect tới URL của file (từ cloudinary)
    res.redirect(attachment.downloadUrl)
  } catch (error) {
    next(error)
  }
}

export const attachmentController = {
  uploadAttachment,
  getAttachments,
  deleteAttachment,
  downloadAttachment,
  previewAttachment
} 