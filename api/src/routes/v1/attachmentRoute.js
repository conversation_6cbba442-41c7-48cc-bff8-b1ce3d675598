/**
 * Attachment routes
 */
import express from 'express'
import { authMiddleware } from '~/middlewares/authMiddleware'
import { attachmentController } from '~/controllers/attachmentController'

const Router = express.Router()

// Delete attachment
Router.route('/:id')
  .delete(authMiddleware.isAuthorized, attachmentController.deleteAttachment)

// Download attachment
Router.route('/:id/download')
  .get(authMiddleware.isAuthorized, attachmentController.downloadAttachment)

// Preview attachment (for images)
Router.route('/:id/preview')
  .get(authMiddleware.isAuthorized, attachmentController.previewAttachment)

export const attachmentRoute = Router 