import { StatusCodes } from 'http-status-codes'
import { attachmentModel } from '~/models/attachmentModel'
import { cardModel } from '~/models/cardModel'
import ApiError from '~/utils/ApiError'
import { CloudinaryProvider } from '~/providers/CloudinaryProvider'

const uploadAttachment = async (cardId, uploadedFile) => {
  try {
    if (!uploadedFile) {
      throw new ApiError(StatusCodes.BAD_REQUEST, 'Không có file để upload')
    }

    // Upload file lên cloudinary sử dụng CloudinaryProvider
    const folderName = `taskflow/attachments/${cardId}`
    const cloudinaryResult = await CloudinaryProvider.streamUpload(uploadedFile.buffer, folderName)

    // Tạo attachment record trong database
    const newAttachment = {
      cardId: cardId,
      fileName: cloudinaryResult.public_id,
      originalName: uploadedFile.originalname,
      fileSize: uploadedFile.size,
      fileType: uploadedFile.mimetype,
      downloadUrl: cloudinaryResult.secure_url
    }

    const createdAttachment = await attachmentModel.createNew(newAttachment)

    // Thêm attachment ID vào card
    await cardModel.pushAttachmentId(cardId, createdAttachment.insertedId)

    // Lấy attachment vừa tạo để trả về
    const fullAttachment = await attachmentModel.findOneById(createdAttachment.insertedId)
    
    return fullAttachment
  } catch (error) {
    console.error('Upload attachment error:', error)
    throw new ApiError(StatusCodes.INTERNAL_SERVER_ERROR, `Có lỗi trong quá trình upload file: ${error.message}`)
  }
}

const getAttachmentsByCardId = async (cardId) => {
  try {
    const attachments = await attachmentModel.findByCardId(cardId)
    return attachments || []
  } catch (error) {
    console.error('Get attachments error:', error)
    throw new ApiError(StatusCodes.INTERNAL_SERVER_ERROR, 'Có lỗi trong quá trình lấy danh sách attachments')
  }
}

const getAttachmentById = async (attachmentId) => {
  try {
    const attachment = await attachmentModel.findOneById(attachmentId)
    if (!attachment) {
      throw new ApiError(StatusCodes.NOT_FOUND, 'Attachment không tồn tại')
    }
    return attachment
  } catch (error) {
    console.error('Get attachment by ID error:', error)
    throw error
  }
}

const deleteAttachment = async (attachmentId) => {
  try {
    // Lấy thông tin attachment trước khi xóa
    const attachment = await attachmentModel.findOneById(attachmentId)
    if (!attachment) {
      throw new ApiError(StatusCodes.NOT_FOUND, 'Attachment không tồn tại')
    }

    // Xóa file trên cloudinary
    await CloudinaryProvider.deleteResource(attachment.fileName)

    // Xóa attachment record trong database
    await attachmentModel.deleteOneById(attachmentId)

    // Xóa attachment ID khỏi card
    await cardModel.pullAttachmentId(attachment.cardId.toString(), attachmentId)

    return { message: 'Xóa attachment thành công' }
  } catch (error) {
    console.error('Delete attachment error:', error)
    throw new ApiError(StatusCodes.INTERNAL_SERVER_ERROR, `Có lỗi trong quá trình xóa attachment: ${error.message}`)
  }
}

export const attachmentService = {
  uploadAttachment,
  getAttachmentsByCardId,
  getAttachmentById,
  deleteAttachment
} 