danh sách yêu cầu phát triển tính năng attachment trong card : 

- người dùng click vào button " attachment "( đã có sẵn ) trong card để sử dụng tính năng 
- mgười dùng có thể upload tệp lên 
- người dùng  có thể xóa tệp khi click vào button icon " xóa " 
- người dùng có thể xem preview file  ( nếu là ảnh ) ( hỗ trợ zoom in/ out )
- tất cả các  file tải lên đều có thể tải xuống : người dùng click vào button  icon ( tải xuống) ở bên phải 
- các tệp tải lên được xếp lần lượt theo : size , ngày tải lên "
- các tệp tải lên sẽ nằm trên phần description 

---

## Danh Sách Task Cụ Thể để Implement Tính Năng Attachment

### **Phase 1: Backend API Development**

**Models & Database:**
- [x] Tạo schema/model cho attachment trong database với các trường: `fileName`, `originalName`, `fileSize`, `fileType`, `uploadDate`, `downloadUrl`, `cardId`
- [x] Cập nhật model Card để bao gồm trường `attachments` (array chứa attachment IDs)

**API Endpoints:**
- [x] Tạo API endpoint `POST /api/v1/cards/:cardId/attachments` để upload file attachment
- [x] Tạo API endpoint `GET /api/v1/cards/:cardId/attachments` để lấy danh sách attachments của card
- [x] Tạo API endpoint `DELETE /api/v1/attachments/:attachmentId` để xóa attachment
- [x] Tạo API endpoint `GET /api/v1/attachments/:attachmentId/download` để download file
- [x] Tạo API endpoint `GET /api/v1/attachments/:attachmentId/preview` để preview file (cho ảnh)

**File Storage:**
- [x] Setup file storage system (sử dụng cloudinary ( dự án đăng sử dụng ))
- [x] Implement file upload middleware với validation (size, type)
- [x] Implement file deletion logic
- [x] Implement file serving logic cho download và preview

### **Phase 2: Frontend Components Development**

**Attachment Upload Component:**
- [x] Tạo component `AttachmentUpload` với drag & drop functionality
- [x] Implement file validation (size, type) ở frontend
- [x] Tạo progress bar hiển thị quá trình upload
- [x] Handle multiple file upload

**Attachment List Component:**
- [x] Tạo component `AttachmentList` để hiển thị danh sách attachments
- [x] Implement sorting theo size và ngày upload
- [x] Tạo item component `AttachmentItem` cho mỗi file attachment
- [x] Hiển thị thông tin file: tên, size, ngày upload, type icon

**Attachment Actions:**
- [x] Tạo button "Xóa" với icon và confirmation dialog
- [x] Tạo button "Tải xuống" với icon
- [x] Implement preview functionality cho file ảnh (zoom in/out)
- [x] Tạo component `AttachmentPreviewModal` cho preview ảnh với zoom (Tái sử dụng ImageLightbox)

### **Phase 3: Integration với Modal ActiveCard**

**UI Layout:**
- [x] Thêm section "Attachments" vào `ActiveCard.jsx` phía trên phần description
- [x] Update layout để attachment section nằm đúng vị trí yêu cầu
- [x] Implement responsive design cho attachment section

**Functionality Integration:**
- [x] Kết nối button "Attachment" trong sidebar với upload functionality  
- [x] Integrate `AttachmentUpload` component vào modal
- [x] Integrate `AttachmentList` component vào modal
- [x] Update Redux state để manage attachments data

**State Management:**
- [x] Thêm attachment-related actions vào Redux (upload, delete, fetch)
- [x] Update `activeCardSlice` để handle attachment state
- [x] Implement loading states cho các attachment operations

### **Phase 4: Card Display Updates**

**Card Component Updates:**
- [x] Update component `Card.jsx` để hiển thị số lượng attachments
- [x] Đảm bảo attachment icon và count hiển thị chính xác
- [x] Update `shouldShowCardActions` logic nếu cần

**Attachment Counter:**
- [x] Implement real-time update attachment count khi upload/delete
- [x] Sync attachment count giữa modal và card display

### **Phase 5: File Processing & Utilities**

**File Utilities:**
- [ ] Tạo utility functions để format file size (KB, MB, GB)
- [ ] Tạo utility để determine file type và hiển thị appropriate icon
- [ ] Implement thumbnail generation cho file ảnh
- [ ] Tạo utility để validate file types được phép upload

**Image Processing:**
- [ ] Implement image resize/compress trước khi upload (nếu cần)
- [ ] Tạo thumbnail cho image preview
- [ ] Setup image optimization

### **Phase 6: Error Handling & Validation**

**Error Handling:**
- [ ] Implement comprehensive error handling cho file upload failures
- [ ] Handle file size exceeded errors
- [ ] Handle unsupported file type errors
- [ ] Implement retry mechanism cho failed uploads

**Validation:**
- [ ] Client-side validation cho file size, type
- [ ] Server-side validation và sanitization
- [ ] Implement file content validation (security)

### **Phase 7: Performance & Optimization**

**Performance:**
- [ ] Implement lazy loading cho attachment list
- [ ] Add pagination nếu có nhiều attachments
- [ ] Optimize file upload performance (chunked upload cho file lớn)
- [ ] Implement caching cho file previews

**User Experience:**
- [ ] Add loading spinners cho all attachment operations
- [ ] Implement toast notifications cho thành công/lỗi
- [ ] Add confirmation dialogs cho delete operations
- [ ] Implement keyboard shortcuts cho attachment actions

### **Phase 8: Testing & Documentation**

**Testing:**
- [ ] Unit tests cho attachment components
- [ ] Integration tests cho file upload/download flow
- [ ] End-to-end tests cho attachment functionality
- [ ] Performance testing với large files

**Documentation:**
- [ ] API documentation cho attachment endpoints
- [ ] Component documentation
- [ ] User guide cho attachment features
- [ ] Security considerations documentation