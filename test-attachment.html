<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Attachment Upload</title>
</head>
<body>
    <h1>Test Attachment Upload</h1>
    <form id="uploadForm">
        <input type="file" id="fileInput" accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip">
        <button type="submit">Upload</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a file');
                return;
            }
            
            const formData = new FormData();
            formData.append('attachment', file);
            
            try {
                // Replace with actual cardId
                const cardId = '6747b8b5b5b5b5b5b5b5b5b5'; // Example cardId
                
                const response = await fetch(`http://localhost:8016/v1/cards/${cardId}/attachments`, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        // Add authorization header if needed
                        // 'Authorization': 'Bearer your-token-here'
                    }
                });
                
                const result = await response.json();
                document.getElementById('result').innerHTML = `
                    <h3>Response:</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
                
                if (!response.ok) {
                    console.error('Upload failed:', result);
                } else {
                    console.log('Upload successful:', result);
                }
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = `
                    <h3>Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        });
    </script>
</body>
</html>
