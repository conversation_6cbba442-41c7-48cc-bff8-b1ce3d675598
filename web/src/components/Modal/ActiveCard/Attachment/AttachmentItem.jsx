import { useState } from 'react'
import { Box, Typography, IconButton, Tooltip, Link, CircularProgress, Menu, MenuItem, ListItemIcon, ListItemText, Chip } from '@mui/material'
import { styled } from '@mui/material/styles'
import DownloadIcon from '@mui/icons-material/Download'
import DeleteIcon from '@mui/icons-material/Delete'
import VisibilityIcon from '@mui/icons-material/Visibility'
import MoreVertIcon from '@mui/icons-material/MoreVert'
import moment from 'moment'
import { useConfirm } from 'material-ui-confirm'
import { toast } from 'react-toastify'
import { 
  formatFileSize, 
  getFileTypeIcon, 
  getFileTypeName, 
  getFileExtension,
  canPreviewFile,
  isImageFile 
} from '~/utils/validators'
import { API_ROOT } from '~/utils/constants'
import FileTypeIcon from './FileTypeIcon'

const AttachmentItemWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(1.5, 2),
  borderRadius: '8px',
  marginBottom: theme.spacing(1.5),
  backgroundColor: theme.palette.background.paper,
  boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    boxShadow: '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)',
    transform: 'translateY(-1px)'
  }
}))

const FileInfo = styled(Box)({
  flexGrow: 1,
  overflow: 'hidden',
  marginLeft: '16px'
})

const FileName = styled(Typography)({
  fontWeight: '500',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis'
})

const FileMeta = styled(Typography)(({ theme }) => ({
  fontSize: '0.8rem',
  color: theme.palette.text.secondary
}))

const ActionButtons = styled(Box)({
  marginLeft: 'auto',
  display: 'flex',
  alignItems: 'center'
})

function AttachmentItem({ attachment, onDelete, onPreview, isDeleting = false }) {
  const [anchorEl, setAnchorEl] = useState(null)
  const confirmDelete = useConfirm()

  const fileType = getFileTypeIcon(attachment.fileType)
  const fileTypeName = getFileTypeName(attachment.fileType)
  const fileExtension = getFileExtension(attachment.originalName)
  const canPreview = canPreviewFile(attachment.fileType)
  const isImage = isImageFile(attachment.fileType)

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
  }

  const handleDelete = () => {
    handleMenuClose()
    confirmDelete({
      title: 'Xác nhận xóa',
      description: `Bạn có chắc chắn muốn xóa file "${attachment.originalName}"? Hành động này không thể hoàn tác.`,
      confirmationText: 'Xóa',
      cancellationText: 'Hủy',
      dialogProps: { maxWidth: 'xs' },
      confirmationButtonProps: { color: 'error', variant: 'contained' },
      cancellationButtonProps: { color: 'inherit' }
    })
      .then(async () => {
        try {
          await onDelete(attachment._id)
          toast.success('File đã được xóa thành công!')
        } catch (error) {
          toast.error('Lỗi khi xóa file. Vui lòng thử lại.')
          console.error('Delete error:', error)
        }
      })
      .catch(() => { /* User cancelled */ })
  }

  const handleDownload = () => {
    handleMenuClose()
    window.open(`${API_ROOT}/v1/attachments/${attachment._id}/download`, '_blank')
  }

  const handlePreview = () => {
    handleMenuClose()
    if (fileType === 'image') {
      onPreview(attachment)
    } else if (canPreview) {
      // Mở file trong tab mới để preview
      window.open(attachment.downloadUrl, '_blank')
    } else {
      // Fallback to download cho file không preview được
      handleDownload()
    }
  }

  return (
    <AttachmentItemWrapper>
      {/* Enhanced File Icon */}
      <FileTypeIcon
        fileType={attachment.fileType}
        fileName={attachment.originalName}
        isImage={isImage}
        imageUrl={isImage ? attachment.downloadUrl : null}
        size="medium"
        showExtension={!isImage}
      />

      <FileInfo>
        <Tooltip title={attachment.originalName} placement="top-start">
          <FileName variant="body1">
            <Link
              href="#"
              onClick={(e) => {
                e.preventDefault()
                handlePreview()
              }}
              underline="hover"
              color="inherit"
            >
              {attachment.originalName}
            </Link>
          </FileName>
        </Tooltip>
        
        {/* Enhanced file metadata */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap', mt: 0.5 }}>
          <Chip 
            label={fileExtension.toUpperCase()} 
            size="small" 
            variant="outlined" 
            sx={{ fontSize: '0.7rem', height: '20px' }}
          />
          <Chip 
            label={formatFileSize(attachment.fileSize)} 
            size="small" 
            variant="outlined"
            sx={{ fontSize: '0.7rem', height: '20px' }}
          />
          {isImage && (
            <Chip 
              label="Ảnh" 
              size="small" 
              color="success"
              variant="outlined"
              sx={{ fontSize: '0.7rem', height: '20px' }}
            />
          )}
        </Box>
        
        <FileMeta>
          {`Tải lên vào ${moment(attachment.uploadDate).format('DD/MM/YYYY, HH:mm')} • ${fileTypeName}`}
        </FileMeta>
      </FileInfo>

      <ActionButtons>
        {isDeleting ? (
          <CircularProgress size={24} sx={{ mx: 1 }} />
        ) : (
          <Tooltip title="Tùy chọn">
            <IconButton onClick={handleMenuOpen} size="small">
              <MoreVertIcon />
            </IconButton>
          </Tooltip>
        )}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          <MenuItem onClick={handlePreview}>
            <ListItemIcon>
              {fileType === 'image' ? 
                <VisibilityIcon fontSize="small" /> : 
                canPreview ? <VisibilityIcon fontSize="small" /> : <DownloadIcon fontSize="small" />
              }
            </ListItemIcon>
            <ListItemText>
              {fileType === 'image' ? 'Xem trước' : canPreview ? 'Xem file' : 'Mở file'}
            </ListItemText>
          </MenuItem>
          <MenuItem onClick={handleDownload}>
            <ListItemIcon>
              <DownloadIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Tải xuống</ListItemText>
          </MenuItem>
          <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
            <ListItemIcon>
              <DeleteIcon fontSize="small" sx={{ color: 'error.main' }} />
            </ListItemIcon>
            <ListItemText>Xóa</ListItemText>
          </MenuItem>
        </Menu>
      </ActionButtons>
    </AttachmentItemWrapper>
  )
}

export default AttachmentItem 