import { useState, useEffect } from 'react'
import { Box, Typography, Select, MenuItem, FormControl, InputLabel, CircularProgress, Alert } from '@mui/material'
import AttachmentItem from './AttachmentItem'
import { getAttachmentsAPI, deleteAttachmentAPI } from '~/apis'
import { toast } from 'react-toastify'
import ImageLightbox from '~/components/Modal/ImageLightbox/ImageLightbox'
import { useDispatch, useSelector } from 'react-redux'
import { 
  selectAttachmentLoading, 
  setAttachmentFetching, 
  setAttachmentDeleting,
  selectCurrentActiveCard
} from '~/redux/activeCard/activeCardSlice'

function AttachmentList({ cardId, onAttachmentDeleted }) {
  const dispatch = useDispatch()
  const attachmentLoading = useSelector(selectAttachmentLoading)
  const activeCard = useSelector(selectCurrentActiveCard)
  
  const [attachments, setAttachments] = useState([])
  const [error, setError] = useState(null)
  const [sortBy, setSortBy] = useState('uploadDate_desc') // Default sort: newest first

  const [lightboxOpen, setLightboxOpen] = useState(false)
  const [lightboxImage, setLightboxImage] = useState('')

  // Fetch attachments when component mounts or cardId changes
  useEffect(() => {
    const fetchAttachments = async () => {
      if (!cardId) {
        console.log('AttachmentList: No cardId provided')
        return
      }
      
      console.log('AttachmentList: Fetching attachments for cardId:', cardId)
      dispatch(setAttachmentFetching(true))
      setError(null)
      
      try {
        const data = await getAttachmentsAPI(cardId)
        console.log('AttachmentList: Attachments fetched:', data)
        setAttachments(data || [])
      } catch (err) {
        console.error('AttachmentList: Fetch attachments error:', err)
        setError('Không thể tải danh sách file đính kèm. Vui lòng thử lại.')
        toast.error('Lỗi khi tải file đính kèm.')
      }
      
      dispatch(setAttachmentFetching(false))
    }
    fetchAttachments()
  }, [cardId, dispatch])

  // Refresh attachment list khi activeCard attachmentIds thay đổi
  useEffect(() => {
    if (activeCard?.attachmentIds && cardId) {
      // Refetch nếu có thay đổi trong attachmentIds
      const fetchLatestAttachments = async () => {
        try {
          const data = await getAttachmentsAPI(cardId)
          setAttachments(data || [])
        } catch (err) {
          console.error('Refresh attachments error:', err)
        }
      }
      fetchLatestAttachments()
    }
  }, [activeCard?.attachmentIds, cardId])

  const handleSortChange = (event) => {
    setSortBy(event.target.value)
  }

  const sortedAttachments = [...attachments].sort((a, b) => {
    const [field, order] = sortBy.split('_')
    let comparison = 0

    if (field === 'uploadDate') {
      comparison = new Date(a.uploadDate) - new Date(b.uploadDate)
    } else if (field === 'fileSize') {
      comparison = a.fileSize - b.fileSize
    } else if (field === 'originalName') {
      comparison = a.originalName.localeCompare(b.originalName)
    }

    return order === 'asc' ? comparison : -comparison
  })

  const handleDeleteAttachment = async (attachmentId) => {
    dispatch(setAttachmentDeleting(true))
    
    try {
      await deleteAttachmentAPI(attachmentId)
      setAttachments(prev => prev.filter(att => att._id !== attachmentId))
      if (onAttachmentDeleted) {
        onAttachmentDeleted(attachmentId)
      }
      return Promise.resolve()
    } catch (err) {
      toast.error('Lỗi khi xóa file.')
      console.error('Delete attachment error:', err)
      return Promise.reject(err)
    } finally {
      dispatch(setAttachmentDeleting(false))
    }
  }

  const handlePreviewImage = (attachment) => {
    setLightboxImage(attachment.downloadUrl)
    setLightboxOpen(true)
  }

  if (attachmentLoading.isFetching) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', my: 3 }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Đang tải file đính kèm...</Typography>
      </Box>
    )
  }

  if (error) {
    return <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>
  }

  return (
    <Box sx={{ mt: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="subtitle1" fontWeight="600">
          Danh sách file ({attachments.length})
        </Typography>
        <FormControl size="small" sx={{ minWidth: 180 }}>
          <InputLabel id="sort-by-label">Sắp xếp theo</InputLabel>
          <Select
            labelId="sort-by-label"
            value={sortBy}
            label="Sắp xếp theo"
            onChange={handleSortChange}
          >
            <MenuItem value="uploadDate_desc">Ngày tải lên (Mới nhất)</MenuItem>
            <MenuItem value="uploadDate_asc">Ngày tải lên (Cũ nhất)</MenuItem>
            <MenuItem value="fileSize_desc">Kích thước (Lớn nhất)</MenuItem>
            <MenuItem value="fileSize_asc">Kích thước (Nhỏ nhất)</MenuItem>
            <MenuItem value="originalName_asc">Tên file (A-Z)</MenuItem>
            <MenuItem value="originalName_desc">Tên file (Z-A)</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {sortedAttachments.length === 0 && !attachmentLoading.isFetching && (
        <Typography sx={{ textAlign: 'center', color: 'text.secondary', my: 3 }}>
          Chưa có file đính kèm nào.
        </Typography>
      )}

      {sortedAttachments.map((attachment) => (
        <AttachmentItem 
          key={attachment._id} 
          attachment={attachment} 
          onDelete={handleDeleteAttachment}
          onPreview={handlePreviewImage}
          isDeleting={attachmentLoading.isDeleting}
        />
      ))}

      {lightboxOpen && (
        <ImageLightbox
          isOpen={lightboxOpen}
          imageSrc={lightboxImage}
          onClose={() => setLightboxOpen(false)}
        />
      )}
    </Box>
  )
}

export default AttachmentList 