import { useState, useRef } from 'react'
import { <PERSON>, <PERSON>po<PERSON>, Button, LinearProgress, Fade, Chip } from '@mui/material'
import CloudUploadIcon from '@mui/icons-material/CloudUpload'
import AttachFileIcon from '@mui/icons-material/AttachFile'
import { styled } from '@mui/material/styles'
import { attachmentFileValidator, isImageFile, getFileTypeName, formatFileSize } from '~/utils/validators'
import { useImageProcessing } from '~/customHooks/useImageProcessing'
import { toast } from 'react-toastify'
import { uploadAttachmentAPI } from '~/apis'
import { useDispatch, useSelector } from 'react-redux'
import { 
  selectAttachmentLoading, 
  setAttachmentUploading 
} from '~/redux/activeCard/activeCardSlice'

// Styled component for the drag & drop area
const UploadArea = styled(Box)(({ theme, isDragActive }) => ({
  border: `2px dashed ${isDragActive ? theme.palette.primary.main : theme.palette.divider}`,
  borderRadius: '8px',
  padding: '24px',
  textAlign: 'center',
  cursor: 'pointer',
  backgroundColor: isDragActive ? theme.palette.action.hover : 'transparent',
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: theme.palette.action.hover
  }
}))

// Hidden input styled component
const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1
})

function AttachmentUpload({ cardId, onUploadSuccess }) {
  const dispatch = useDispatch()
  const attachmentLoading = useSelector(selectAttachmentLoading)
  const { compressImageFile, isProcessing: isImageProcessing } = useImageProcessing()
  
  const [isDragActive, setIsDragActive] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [selectedFile, setSelectedFile] = useState(null)
  const [compressionInfo, setCompressionInfo] = useState(null)
  const fileInputRef = useRef(null)

  // Drag & drop handlers
  const handleDragEnter = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragActive(true)
  }

  const handleDragLeave = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragActive(false)
  }

  const handleDragOver = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragActive(true)
  }

  const handleDrop = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragActive(false)
    
    const files = e.dataTransfer.files
    if (files && files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  // Simulate progress during upload
  const simulateProgress = () => {
    setUploadProgress(0)
    const timer = setInterval(() => {
      setUploadProgress((oldProgress) => {
        if (oldProgress >= 95) {
          clearInterval(timer)
          return 95
        }
        return Math.min(oldProgress + 10, 95)
      })
    }, 300)
    return timer
  }

  // Enhanced file upload với image compression
  const handleFileUpload = async (file) => {
    // Validate the file
    const error = attachmentFileValidator(file)
    if (error) {
      toast.error(error)
      return
    }

    setSelectedFile(file)
    let finalFile = file

    // Compress image nếu là file ảnh
    if (isImageFile(file.type)) {
      try {
        toast.info('Đang tối ưu hóa ảnh...', { autoClose: 2000 })
        const compressionResult = await compressImageFile(file, {
          maxWidth: 1920,
          maxHeight: 1080,
          quality: 0.85
        })
        
        finalFile = compressionResult.file
        setCompressionInfo(compressionResult)
        
        if (compressionResult.compressionRatio > 0) {
          toast.success(`Đã tối ưu ảnh: giảm ${compressionResult.compressionRatio}% kích thước`, { autoClose: 3000 })
        }
      } catch (compressionError) {
        console.warn('Image compression failed, using original file:', compressionError)
        toast.warning('Không thể tối ưu ảnh, sử dụng file gốc')
      }
    }

    dispatch(setAttachmentUploading(true))
    const progressTimer = simulateProgress()

    try {
      // Create FormData
      const formData = new FormData()
      formData.append('attachment', finalFile)

      // Call API to upload
      const result = await uploadAttachmentAPI(cardId, formData)
      
      // Set progress to 100% when done
      setUploadProgress(100)
      setTimeout(() => {
        clearInterval(progressTimer)
        dispatch(setAttachmentUploading(false))
        setUploadProgress(0)
        setSelectedFile(null)
        setCompressionInfo(null)
        
        // Notify parent component about successful upload
        if (onUploadSuccess) {
          onUploadSuccess(result)
        }
        toast.success('File uploaded successfully!')
      }, 500)
    } catch (error) {
      clearInterval(progressTimer)
      dispatch(setAttachmentUploading(false))
      setUploadProgress(0)
      setSelectedFile(null)
      setCompressionInfo(null)
      toast.error('Upload failed. Please try again.')
      console.error('Upload error:', error)
    }
  }

  // Handler for file input change
  const handleFileInputChange = (e) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileUpload(files[0])
    }
    // Reset the input to allow uploading the same file again
    e.target.value = null
  }

  // Open file dialog when clicking on the upload area
  const handleAreaClick = () => {
    if (!attachmentLoading.isUploading && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  return (
    <Box sx={{ mb: 3 }}>
      <Typography variant="subtitle1" fontWeight="600" sx={{ mb: 1 }}>
        Tải lên file đính kèm
      </Typography>
      
      {/* File Processing Info */}
      {selectedFile && (attachmentLoading.isUploading || isImageProcessing) && (
        <Box sx={{ mb: 2, p: 2, border: '1px solid', borderColor: 'divider', borderRadius: '8px' }}>
          <Typography variant="body2" sx={{ mb: 1 }}>
            <strong>File:</strong> {selectedFile.name}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 1 }}>
            <Chip 
              label={getFileTypeName(selectedFile.type)} 
              size="small" 
              color="primary" 
              variant="outlined" 
            />
            <Chip 
              label={formatFileSize(selectedFile.size)} 
              size="small" 
              variant="outlined" 
            />
          </Box>
          
          {compressionInfo && (
            <Typography variant="body2" color="success.main" sx={{ mb: 1 }}>
              ✓ Đã tối ưu: {formatFileSize(compressionInfo.originalSize)} → {formatFileSize(compressionInfo.compressedSize)} 
              (-{compressionInfo.compressionRatio}%)
            </Typography>
          )}
        </Box>
      )}
      
      {attachmentLoading.isUploading ? (
        <Box sx={{ width: '100%', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
              {isImageProcessing ? 'Đang xử lý ảnh...' : 'Đang tải lên...'}
            </Typography>
            <Typography variant="body2" color="primary">
              {uploadProgress}%
            </Typography>
          </Box>
          <LinearProgress variant="determinate" value={uploadProgress} />
        </Box>
      ) : (
        <Fade in={!attachmentLoading.isUploading}>
          <UploadArea
            isDragActive={isDragActive}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onClick={handleAreaClick}
          >
            <CloudUploadIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
            <Typography variant="body1" gutterBottom>
              Kéo thả file vào đây hoặc click để chọn file
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              Hỗ trợ nhiều định dạng file: ảnh, PDF, Word, Excel, PowerPoint, ZIP,...
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ mb: 2 }}>
              Ảnh sẽ được tự động tối ưu hóa để tiết kiệm dung lượng
            </Typography>
            <Button
              variant="contained"
              startIcon={<AttachFileIcon />}
              sx={{ mt: 2 }}
              onClick={(e) => {
                e.stopPropagation()
                fileInputRef.current.click()
              }}
            >
              Chọn file
            </Button>
            <VisuallyHiddenInput
              ref={fileInputRef}
              type="file"
              onChange={handleFileInputChange}
            />
          </UploadArea>
        </Fade>
      )}
    </Box>
  )
}

export default AttachmentUpload 