import React from 'react'
import { Box, Avatar } from '@mui/material'
import { styled } from '@mui/material/styles'
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile'
import ImageIcon from '@mui/icons-material/Image'
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf'
import DescriptionIcon from '@mui/icons-material/Description'
import LeaderboardIcon from '@mui/icons-material/Leaderboard'
import SlideshowIcon from '@mui/icons-material/Slideshow'
import FolderZipIcon from '@mui/icons-material/FolderZip'
import ArticleIcon from '@mui/icons-material/Article'
import { getFileTypeIcon, getFileExtension } from '~/utils/validators'

const IconWrapper = styled(Box)(({ theme, fileType }) => {
  const getBackgroundColor = () => {
    switch (fileType) {
      case 'image': return '#4CAF50'
      case 'pdf': return '#F44336'
      case 'doc': return '#2196F3'
      case 'excel': return '#4CAF50'
      case 'ppt': return '#FF9800'
      case 'zip': return '#9C27B0'
      case 'txt': return '#607D8B'
      default: return '#757575'
    }
  }

  return {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '48px',
    height: '48px',
    borderRadius: '8px',
    backgroundColor: getBackgroundColor(),
    color: '#fff',
    position: 'relative',
    overflow: 'hidden',
    boxShadow: theme.shadows[2],
    transition: 'all 0.2s ease',
    '&:hover': {
      transform: 'scale(1.05)',
      boxShadow: theme.shadows[4],
    }
  }
})

const ExtensionBadge = styled(Box)(({ theme }) => ({
  position: 'absolute',
  bottom: '-2px',
  right: '-2px',
  backgroundColor: 'rgba(0, 0, 0, 0.7)',
  color: '#fff',
  fontSize: '8px',
  fontWeight: 'bold',
  padding: '1px 4px',
  borderRadius: '4px',
  textTransform: 'uppercase',
  lineHeight: 1.2,
  minWidth: '16px',
  textAlign: 'center'
}))

const renderFileIcon = (fileType, size = 'medium') => {
  const iconSize = size === 'small' ? 16 : size === 'large' ? 32 : 24

  switch (fileType) {
    case 'image':
      return <ImageIcon sx={{ fontSize: iconSize }} />
    case 'pdf':
      return <PictureAsPdfIcon sx={{ fontSize: iconSize }} />
    case 'doc':
      return <DescriptionIcon sx={{ fontSize: iconSize }} />
    case 'excel':
      return <LeaderboardIcon sx={{ fontSize: iconSize }} />
    case 'ppt':
      return <SlideshowIcon sx={{ fontSize: iconSize }} />
    case 'zip':
      return <FolderZipIcon sx={{ fontSize: iconSize }} />
    case 'txt':
      return <ArticleIcon sx={{ fontSize: iconSize }} />
    default:
      return <InsertDriveFileIcon sx={{ fontSize: iconSize }} />
  }
}

function FileTypeIcon({ fileType, fileName, size = 'medium', showExtension = true, isImage = false, imageUrl = null }) {
  const detectedFileType = getFileTypeIcon(fileType)
  const extension = getFileExtension(fileName)

  // Nếu là ảnh và có URL, hiển thị thumbnail
  if (isImage && imageUrl) {
    return (
      <Avatar
        src={imageUrl}
        alt={fileName}
        variant="rounded"
        sx={{
          width: size === 'small' ? 32 : size === 'large' ? 64 : 48,
          height: size === 'small' ? 32 : size === 'large' ? 64 : 48,
          borderRadius: '8px',
          boxShadow: (theme) => theme.shadows[2],
          transition: 'all 0.2s ease',
          '&:hover': {
            transform: 'scale(1.05)',
            boxShadow: (theme) => theme.shadows[4],
          }
        }}
      >
        {renderFileIcon(detectedFileType, size)}
      </Avatar>
    )
  }

  return (
    <IconWrapper fileType={detectedFileType}>
      {renderFileIcon(detectedFileType, size)}
      {showExtension && extension && (
        <ExtensionBadge>
          {extension.length > 4 ? extension.slice(0, 3) + '.' : extension}
        </ExtensionBadge>
      )}
    </IconWrapper>
  )
}

export default FileTypeIcon 