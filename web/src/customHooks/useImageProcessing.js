import { useState, useCallback } from 'react'
import { 
  generateImageThumbnail, 
  compressImage, 
  isImageFile,
  validateFileName,
  sanitizeFileName 
} from '~/utils/validators'

/**
 * Custom hook để xử lý image processing và file utilities
 */
export const useImageProcessing = () => {
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState(null)

  /**
   * Generate thumbnail cho image file
   */
  const generateThumbnail = useCallback(async (file, options = {}) => {
    const { maxWidth = 300, maxHeight = 300, quality = 0.8 } = options
    
    setIsProcessing(true)
    setError(null)

    try {
      if (!isImageFile(file.type)) {
        throw new Error('File không phải là ảnh')
      }

      const thumbnailBlob = await generateImageThumbnail(file, maxWidth, maxHeight, quality)
      
      // Tạo URL object cho preview
      const thumbnailUrl = URL.createObjectURL(thumbnailBlob)
      
      return {
        blob: thumbnailBlob,
        url: thumbnailUrl,
        size: thumbnailBlob.size
      }
    } catch (err) {
      setError(err.message)
      throw err
    } finally {
      setIsProcessing(false)
    }
  }, [])

  /**
   * Compress image trước khi upload
   */
  const compressImageFile = useCallback(async (file, options = {}) => {
    const { maxWidth = 1920, maxHeight = 1080, quality = 0.8 } = options
    
    setIsProcessing(true)
    setError(null)

    try {
      const compressedFile = await compressImage(file, maxWidth, maxHeight, quality)
      
      return {
        file: compressedFile,
        originalSize: file.size,
        compressedSize: compressedFile.size,
        compressionRatio: ((file.size - compressedFile.size) / file.size * 100).toFixed(1)
      }
    } catch (err) {
      setError(err.message)
      throw err
    } finally {
      setIsProcessing(false)
    }
  }, [])

  /**
   * Process multiple images (thumbnail + compression)
   */
  const processImages = useCallback(async (files, options = {}) => {
    const { 
      generateThumbnails = true,
      compressImages = true,
      thumbnailOptions = {},
      compressionOptions = {}
    } = options

    setIsProcessing(true)
    setError(null)

    try {
      const results = []

      for (const file of files) {
        if (!isImageFile(file.type)) {
          results.push({
            original: file,
            error: 'File không phải là ảnh'
          })
          continue
        }

        const result = { original: file }

        // Generate thumbnail nếu được yêu cầu
        if (generateThumbnails) {
          try {
            result.thumbnail = await generateThumbnail(file, thumbnailOptions)
          } catch (err) {
            result.thumbnailError = err.message
          }
        }

        // Compress image nếu được yêu cầu
        if (compressImages) {
          try {
            result.compressed = await compressImageFile(file, compressionOptions)
          } catch (err) {
            result.compressionError = err.message
          }
        }

        results.push(result)
      }

      return results
    } catch (err) {
      setError(err.message)
      throw err
    } finally {
      setIsProcessing(false)
    }
  }, [generateThumbnail, compressImageFile])

  /**
   * Validate và sanitize filename
   */
  const processFileName = useCallback((filename) => {
    const validation = validateFileName(filename)
    if (validation) {
      return { error: validation, sanitized: null }
    }

    const sanitized = sanitizeFileName(filename)
    return { error: null, sanitized }
  }, [])

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    isProcessing,
    error,
    generateThumbnail,
    compressImageFile,
    processImages,
    processFileName,
    clearError
  }
} 