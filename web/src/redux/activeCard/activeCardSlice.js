import { createSlice } from '@reduxjs/toolkit'

// Khởi tạo giá trị của một Slice trong redux
const initialState = {
  currentActiveCard: null,
  isShowModalActiveCard: false,
  // Thêm attachment loading states
  attachmentLoading: {
    isUploading: false,
    isFetching: false,
    isDeleting: false
  }
}

// Khởi tạo một slice trong kho lưu trữ - redux store
export const activeCardSlice = createSlice({
  name: 'activeCard',
  initialState,
  // Reducers: Nơi xử lý dữ liệu đồng bộ
  reducers: {
    // Lưu ý luôn là ở đây cần cặp ngoặc nhọn cho function trong reducer cho dù code bên trong chỉ có 1 dòng, đây là rule của Redux
    // https://redux-toolkit.js.org/usage/immer-reducers#mutating-and-returning-state
    showModalActiveCard: (state) => {
      state.isShowModalActiveCard = true
    },

    // Clear data và đóng modal ActiveCard
    clearAndHideCurrentActiveCard: (state) => {
      state.currentActiveCard = null
      state.isShowModalActiveCard = false
      // Reset attachment loading states khi đóng modal
      state.attachmentLoading = {
        isUploading: false,
        isFetching: false,
        isDeleting: false
      }
    },

    updateCurrentActiveCard: (state, action) => {
      const fullCard = action.payload // action.payload là chuẩn đặt tên nhận dữ liệu vào reducer, ở đây chúng ta gán nó ra một biến có nghĩa hơn
      // xử lý dữ liệu nếu cần thiết
      //...
      
      // Đảm bảo attachment data consistency
      if (fullCard && fullCard.attachmentIds && !fullCard.attachments) {
        // Tạo attachments stub array cho display count
        fullCard.attachments = Array(fullCard.attachmentIds.length).fill(null)
      }
      
      // Update lại dữ liệu currentActiveCard trong Redux
      state.currentActiveCard = fullCard
    },

    // Attachment loading actions
    setAttachmentUploading: (state, action) => {
      state.attachmentLoading.isUploading = action.payload
    },

    setAttachmentFetching: (state, action) => {
      state.attachmentLoading.isFetching = action.payload
    },

    setAttachmentDeleting: (state, action) => {
      state.attachmentLoading.isDeleting = action.payload
    },

    // Attachment CRUD actions
    addAttachmentToCard: (state, action) => {
      const newAttachment = action.payload
      if (state.currentActiveCard) {
        // Đảm bảo attachmentIds tồn tại
        if (!state.currentActiveCard.attachmentIds) {
          state.currentActiveCard.attachmentIds = []
        }
        // Thêm attachment ID vào mảng
        state.currentActiveCard.attachmentIds.push(newAttachment._id)
        
        // Cập nhật attachment count cho display
        if (!state.currentActiveCard.attachments) {
          state.currentActiveCard.attachments = []
        }
        state.currentActiveCard.attachments.push(newAttachment)
      }
    },

    removeAttachmentFromCard: (state, action) => {
      const attachmentId = action.payload
      if (state.currentActiveCard) {
        // Remove từ attachmentIds array
        if (state.currentActiveCard.attachmentIds) {
          state.currentActiveCard.attachmentIds = state.currentActiveCard.attachmentIds.filter(
            id => id !== attachmentId
          )
        }
        
        // Remove từ attachments array cho display count
        if (state.currentActiveCard.attachments) {
          state.currentActiveCard.attachments = state.currentActiveCard.attachments.filter(
            att => att._id !== attachmentId
          )
        }
      }
    },

    // Sync attachment count từ attachmentIds
    syncAttachmentCount: (state) => {
      if (state.currentActiveCard && state.currentActiveCard.attachmentIds) {
        // Đảm bảo attachments array phản ánh đúng số lượng cho display
        const attachmentCount = state.currentActiveCard.attachmentIds.length
        if (!state.currentActiveCard.attachments) {
          state.currentActiveCard.attachments = []
        }
        // Tạo attachments stub array để display count chính xác
        // Chỉ cần length, không cần full data
        state.currentActiveCard.attachments = Array(attachmentCount).fill(null)
      }
    }
  },
  // ExtraReducers: Xử lý dữ liệu bất đồng bộ
  // eslint-disable-next-line no-unused-vars
  extraReducers: (builder) => {}
})

// Action creators are generated for each case reducer function
// Actions: Là nơi dành cho các components bên dưới gọi bằng dispatch() tới nó để cập nhật lại dữ liệu thông qua reducer (chạy đồng bộ)
// Để ý ở trên thì không thấy properties actions đâu cả, bởi vì những cái actions này đơn giản là được thằng redux tạo tự động theo tên của reducer nhé.
export const {
  clearAndHideCurrentActiveCard,
  updateCurrentActiveCard,
  showModalActiveCard,
  setAttachmentUploading,
  setAttachmentFetching,
  setAttachmentDeleting,
  addAttachmentToCard,
  removeAttachmentFromCard,
  syncAttachmentCount
} = activeCardSlice.actions

// Selectors: Là nơi dành cho các components bên dưới gọi bằng hook useSelector() để lấy dữ liệu từ trong kho redux store ra sử dụng
export const selectCurrentActiveCard = (state) => {
  return state.activeCard.currentActiveCard
}

export const selectIsShowModalActiveCard = (state) => {
  return state.activeCard.isShowModalActiveCard
}

export const selectAttachmentLoading = (state) => {
  return state.activeCard.attachmentLoading
}

// Cái file này tên là activeCardSlice NHƯNG chúng ta sẽ export một thứ tên là Reducer, mọi người lưu ý :D
// export default activeCardSlice.reducer
export const activeCardReducer = activeCardSlice.reducer
