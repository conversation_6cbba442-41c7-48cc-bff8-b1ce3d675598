/**
 * TrungQuanDev: https://youtube.com/@trungquandev
 */

// Một vài biểu thức chính quy - Regular Expression và custom message.
// Về Regular Expression khá hại não: https://viblo.asia/p/hoc-regular-expression-va-cuoc-doi-ban-se-bot-kho-updated-v22-Az45bnoO5xY
export const FIELD_REQUIRED_MESSAGE = 'This field is required.'
export const EMAIL_RULE = /^\S+@\S+\.\S+$/
export const EMAIL_RULE_MESSAGE = 'Email is invalid. (<EMAIL>)'
export const PASSWORD_RULE = /^(?=.*[a-zA-Z])(?=.*\d)[A-Za-z\d\W]{8,256}$/
export const PASSWORD_RULE_MESSAGE = 'Password must include at least 1 letter, a number, and at least 8 characters.'


// Liên quan đến Validate File
export const LIMIT_COMMON_FILE_SIZE = 10485760 // byte = 10 MB
export const ALLOW_COMMON_FILE_TYPES = ['image/jpg', 'image/jpeg', 'image/png']

// Constants cho attachment files
export const ATTACHMENT_MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
export const ATTACHMENT_MAX_IMAGE_SIZE = 5 * 1024 * 1024 // 5MB cho ảnh
export const ATTACHMENT_MAX_DOCUMENT_SIZE = 10 * 1024 * 1024 // 10MB cho documents

// File type categories
export const IMAGE_TYPES = ['image/jpg', 'image/jpeg', 'image/png', 'image/gif', 'image/webp']
export const DOCUMENT_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain'
]
export const SPREADSHEET_TYPES = [
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/csv'
]
export const PRESENTATION_TYPES = [
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation'
]
export const ARCHIVE_TYPES = [
  'application/zip',
  'application/x-zip-compressed',
  'application/x-rar-compressed',
  'application/x-7z-compressed'
]

/**
 * Validation cho single file: Front-end làm bước check nhẹ, còn chủ yếu là Back-end phải check
 */
export const singleFileValidator = (file) => {
  if (!file) return 'Please select a file!'

  if (!ALLOW_COMMON_FILE_TYPES.includes(file.type)) {
    return 'Invalid file type! We only accept image, document, spreadsheet, pdf, and archive files.'
  }

  if (file.size > LIMIT_COMMON_FILE_SIZE) {
    return 'File size too large! Maximum is 10MB.'
  }

  return null
}

/**
 * Advanced file validation cho attachment với specific rules cho từng loại file
 */
export const attachmentFileValidator = (file) => {
  if (!file) return 'Vui lòng chọn file!'

  // Tất cả file types được phép
  const allAllowedTypes = [
    ...IMAGE_TYPES,
    ...DOCUMENT_TYPES,
    ...SPREADSHEET_TYPES,
    ...PRESENTATION_TYPES,
    ...ARCHIVE_TYPES
  ]

  if (!allAllowedTypes.includes(file.type)) {
    return 'Định dạng file không được hỗ trợ! Chỉ cho phép ảnh, PDF, Word, Excel, PowerPoint, và file nén.'
  }

  // Kiểm tra kích thước file theo loại
  if (IMAGE_TYPES.includes(file.type) && file.size > ATTACHMENT_MAX_IMAGE_SIZE) {
    return 'Ảnh quá lớn! Kích thước tối đa cho ảnh là 5MB.'
  }

  if (file.size > ATTACHMENT_MAX_FILE_SIZE) {
    return 'File quá lớn! Kích thước tối đa là 10MB.'
  }

  return null
}

/**
 * Format file size thành dạng dễ đọc với nhiều đơn vị hơn
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  if (bytes < 0) return 'Invalid size'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  // Đảm bảo không vượt quá array length
  const sizeIndex = Math.min(i, sizes.length - 1)
  const formattedSize = parseFloat((bytes / Math.pow(k, sizeIndex)).toFixed(2))
  
  return `${formattedSize} ${sizes[sizeIndex]}`
}

/**
 * Enhanced file type detection với nhiều categories
 */
export const getFileTypeIcon = (fileType) => {
  if (IMAGE_TYPES.includes(fileType)) return 'image'
  if (fileType.includes('pdf')) return 'pdf'
  if (DOCUMENT_TYPES.includes(fileType)) return 'doc'
  if (SPREADSHEET_TYPES.includes(fileType)) return 'excel'
  if (PRESENTATION_TYPES.includes(fileType)) return 'ppt'
  if (ARCHIVE_TYPES.includes(fileType)) return 'zip'
  if (fileType.includes('text/plain')) return 'txt'
  
  return 'file' // Default icon
}

/**
 * Get file extension từ filename
 */
export const getFileExtension = (filename) => {
  if (!filename) return ''
  const lastDotIndex = filename.lastIndexOf('.')
  return lastDotIndex !== -1 ? filename.slice(lastDotIndex + 1).toLowerCase() : ''
}

/**
 * Check if file is image
 */
export const isImageFile = (fileType) => {
  return IMAGE_TYPES.includes(fileType)
}

/**
 * Check if file can be previewed in browser
 */
export const canPreviewFile = (fileType) => {
  const previewableTypes = [
    ...IMAGE_TYPES,
    'application/pdf',
    'text/plain'
  ]
  return previewableTypes.includes(fileType)
}

/**
 * Generate thumbnail từ image file
 */
export const generateImageThumbnail = (file, maxWidth = 300, maxHeight = 300, quality = 0.8) => {
  return new Promise((resolve, reject) => {
    if (!isImageFile(file.type)) {
      reject(new Error('File không phải là ảnh'))
      return
    }

    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      // Tính toán kích thước mới giữ nguyên tỷ lệ
      let { width, height } = img
      
      if (width > height) {
        if (width > maxWidth) {
          height = height * (maxWidth / width)
          width = maxWidth
        }
      } else {
        if (height > maxHeight) {
          width = width * (maxHeight / height)
          height = maxHeight
        }
      }

      // Set canvas size
      canvas.width = width
      canvas.height = height

      // Draw và resize image
      ctx.drawImage(img, 0, 0, width, height)

      // Convert to blob
      canvas.toBlob(resolve, 'image/jpeg', quality)
    }

    img.onerror = () => reject(new Error('Không thể load ảnh'))
    img.src = URL.createObjectURL(file)
  })
}

/**
 * Compress image trước khi upload
 */
export const compressImage = async (file, maxWidth = 1920, maxHeight = 1080, quality = 0.8) => {
  if (!isImageFile(file.type)) {
    return file // Trả về file gốc nếu không phải ảnh
  }

  try {
    const compressedBlob = await generateImageThumbnail(file, maxWidth, maxHeight, quality)
    
    // Tạo file mới từ compressed blob
    const compressedFile = new File([compressedBlob], file.name, {
      type: 'image/jpeg',
      lastModified: Date.now()
    })

    // Chỉ sử dụng compressed version nếu nhỏ hơn original
    return compressedFile.size < file.size ? compressedFile : file
  } catch (error) {
    console.error('Lỗi khi compress ảnh:', error)
    return file // Trả về file gốc nếu có lỗi
  }
}

/**
 * Validate file name
 */
export const validateFileName = (filename) => {
  if (!filename) return 'Tên file không được để trống'
  if (filename.length > 255) return 'Tên file quá dài (tối đa 255 ký tự)'
  
  // Check for invalid characters
  const invalidChars = /[<>:"/\\|?*]/
  if (invalidChars.test(filename)) {
    return 'Tên file chứa ký tự không hợp lệ'
  }
  
  return null
}

/**
 * Sanitize filename để upload an toàn
 */
export const sanitizeFileName = (filename) => {
  if (!filename) return 'untitled'
  
  // Remove invalid characters và normalize
  return filename
    .replace(/[<>:"/\\|?*]/g, '_') // Replace invalid chars with underscore
    .replace(/\s+/g, '_') // Replace spaces with underscore
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .replace(/^_|_$/g, '') // Remove leading/trailing underscores
    .substring(0, 200) // Limit length
    || 'untitled'
}

/**
 * Get human readable file type name
 */
export const getFileTypeName = (fileType) => {
  const typeMap = {
    'image/jpeg': 'JPEG Image',
    'image/jpg': 'JPEG Image', 
    'image/png': 'PNG Image',
    'image/gif': 'GIF Image',
    'image/webp': 'WebP Image',
    'application/pdf': 'PDF Document',
    'application/msword': 'Word Document',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Document',
    'application/vnd.ms-excel': 'Excel Spreadsheet',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel Spreadsheet',
    'application/vnd.ms-powerpoint': 'PowerPoint Presentation',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint Presentation',
    'text/plain': 'Text File',
    'application/zip': 'ZIP Archive',
    'application/x-zip-compressed': 'ZIP Archive',
    'text/csv': 'CSV File'
  }
  
  return typeMap[fileType] || 'Unknown File Type'
}
